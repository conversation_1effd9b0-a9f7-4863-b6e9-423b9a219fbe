.page-container {
  padding: 32rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
}

.rules-content {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.1);
}

.rules-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2c5aa0;
  margin-bottom: 32rpx;
  text-align: center;
}

/* 章节样式 */
.section {
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #2c5aa0;
  margin-bottom: 16rpx;
  padding-bottom: 8rpx;
  border-bottom: 2rpx solid #e8f4fd;
}

.section-content {
  font-size: 28rpx;
  line-height: 1.8;
  color: #666;
  padding: 16rpx 0;
}

/* 奖励列表样式 */
.reward-list {
  margin-top: 16rpx;
}

.reward-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 20rpx;
  margin-bottom: 12rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border-left: 4rpx solid #2c5aa0;
}

.reward-item.highlight {
  background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
  border-left-color: #ff9800;
}

.reward-item .rank {
  font-weight: bold;
  color: #2c5aa0;
  font-size: 30rpx;
}

.reward-item.highlight .rank {
  color: #ff9800;
}

.reward-item .reward {
  font-weight: bold;
  color: #333;
}

.reward-item.highlight .reward {
  color: #e65100;
}

/* 规则列表样式 */
.rules-list {
  margin-top: 16rpx;
}

.rules-item {
  margin-bottom: 20rpx;
  padding: 16rpx 0;
  line-height: 1.8;
  position: relative;
}

.rules-item .number {
  color: #2c5aa0;
  font-weight: bold;
  margin-right: 8rpx;
}

.rules-item .bold {
  font-weight: bold;
  color: #333;
}

.rules-item.warning {
  background-color: #fff3e0;
  padding: 16rpx;
  border-radius: 8rpx;
  border-left: 4rpx solid #ff9800;
}

.rules-item.warning .number {
  color: #ff9800;
}

/* 子规则样式 */
.sub-rules {
  margin-top: 12rpx;
  padding-left: 24rpx;
}

.sub-item {
  margin-bottom: 8rpx;
  color: #666;
  line-height: 1.6;
}
